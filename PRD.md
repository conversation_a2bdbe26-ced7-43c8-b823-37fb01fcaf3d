# Tay Duong Pharma Website - Product Requirements Document

## Project Overview
A modern, responsive pharmaceutical company website for Tay Duong Pharma, built with Next.js, TypeScript, and Tailwind CSS.

## Current Architecture
- **Framework**: Next.js 14 with App Router
- **Styling**: Tailwind CSS + shadcn/ui components
- **Internationalization**: next-intl (English/Vietnamese)
- **UI Components**: Radix UI primitives with custom styling
- **Typography**: Roboto font family
- **Icons**: Lucide React

## Current Pages & Components
1. **Home Page** (`responsive-pharmacy-home.tsx`)
   - Hero section with rotating background images
   - Company impact statistics
   - Service benefits grid
   - Multiple content sections

2. **Products Page** 
   - Product listing with filtering
   - Individual product detail pages
   - Product cards with tabs for details

3. **Header Component**
   - Logo and navigation
   - Language switcher
   - Responsive design

4. **Footer Component**
   - Company information
   - Contact details
   - Google Maps integration
   - Certifications display

## Current Design System
- **Color Palette**: 
  - Primary: Teal/Cyan tones (#008080, cyan-700, teal-600)
  - Accent: Amber (#fbbf24)
  - Background: White/neutral
- **Typography**: Roboto (400, 700 weights)
- **Border Radius**: 0.5rem default
- **Spacing**: Tailwind standard scale

## Areas for Enhancement
1. **Visual Design Improvements**
2. **User Experience Optimization**
3. **Accessibility Enhancements**
4. **Performance Optimization**
5. **Modern UI Patterns**

## Technical Requirements
- Maintain Next.js 14 architecture
- Preserve internationalization
- Keep responsive design
- Maintain SEO optimization
- Ensure accessibility compliance
